package db

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/kryptogo/kg-solana-data/data-writer/models"
	pb "github.com/kryptogo/kg-solana-data/data-writer/proto"
	"github.com/mr-tron/base58"
)

func (r *Repo) ProcessSolTransfers(ctx context.Context, events []*pb.SolTransferEvent) error {
	if len(events) == 0 {
		return nil
	}

	// Convert events to transfers
	transfers := make([]models.SolTransfer, 0, len(events))
	for _, event := range events {
		fromWallet, err := base58.Decode(event.FromWallet)
		if err != nil {
			return fmt.Errorf("failed to decode from wallet address: %v", err)
		}
		toWallet, err := base58.Decode(event.ToWallet)
		if err != nil {
			return fmt.Errorf("failed to decode to wallet address: %v", err)
		}

		transfers = append(transfers, models.SolTransfer{
			BaseTransfer: models.BaseTransfer{
				Signature:  event.Metadata.Signature,
				IxIndex:    uint16(event.Metadata.IxIndex),
				Slot:       event.Metadata.Slot,
				FromWallet: fromWallet,
				ToWallet:   toWallet,
				Amount:     event.AmountLamports,
			},
		})
	}

	// Process in batches
	for i := 0; i < len(transfers); i += maxBatchSize {
		end := i + maxBatchSize
		if end > len(transfers) {
			end = len(transfers)
		}

		batch := transfers[i:end]
		if err := r.insertSolBatch(ctx, batch); err != nil {
			return fmt.Errorf("failed to insert SOL transfer batch: %v", err)
		}
	}

	return nil
}

func (r *Repo) ProcessTokenTransfers(ctx context.Context, events []*pb.TokenTransferEvent) error {
	if len(events) == 0 {
		return nil
	}

	// Convert events to transfers
	transfers := make([]models.TokenTransfer, 0, len(events))
	for _, event := range events {
		amount, err := strconv.ParseUint(event.Amount, 10, 64)
		if err != nil {
			return fmt.Errorf("failed to parse amount: %v", err)
		}

		fromWallet, err := base58.Decode(event.FromWallet)
		if err != nil {
			return fmt.Errorf("failed to decode from wallet address: %v", err)
		}
		toWallet, err := base58.Decode(event.ToWallet)
		if err != nil {
			return fmt.Errorf("failed to decode to wallet address: %v", err)
		}
		tokenMint, err := base58.Decode(event.TokenMint)
		if err != nil {
			return fmt.Errorf("failed to decode token mint address: %v", err)
		}

		transfers = append(transfers, models.TokenTransfer{
			BaseTransfer: models.BaseTransfer{
				Signature:  event.Metadata.Signature,
				IxIndex:    uint16(event.Metadata.IxIndex),
				Slot:       event.Metadata.Slot,
				FromWallet: fromWallet,
				ToWallet:   toWallet,
				Amount:     amount,
			},
			TokenMint: tokenMint,
		})
	}

	// Process in batches
	for i := 0; i < len(transfers); i += maxBatchSize {
		end := i + maxBatchSize
		if end > len(transfers) {
			end = len(transfers)
		}

		batch := transfers[i:end]
		if err := r.insertTokenBatch(ctx, batch); err != nil {
			return fmt.Errorf("failed to insert token transfer batch: %v", err)
		}
	}

	return nil
}

func (r *Repo) insertSolBatch(ctx context.Context, transfers []models.SolTransfer) error {
	var err error
	for retry := 0; retry < maxRetries; retry++ {
		err = r.WithTransaction(ctx, func(ctx context.Context, tx pgx.Tx) error {
			// Create temporary table
			if _, err := tx.Exec(ctx, "CREATE TEMPORARY TABLE temp_batch (LIKE sol_transfers) ON COMMIT DROP"); err != nil {
				return fmt.Errorf("failed to create temporary table: %v", err)
			}

			// Copy data to temporary table
			rows := make([][]interface{}, len(transfers))
			for i, t := range transfers {
				rows[i] = []interface{}{
					t.Signature,
					t.IxIndex,
					t.Slot,
					t.FromWallet,
					t.ToWallet,
					t.Amount,
				}
			}

			_, err = tx.CopyFrom(ctx,
				pgx.Identifier{"temp_batch"},
				[]string{"signature", "ix_index", "slot", "from_wallet", "to_wallet", "amount"},
				pgx.CopyFromRows(rows))
			if err != nil {
				return fmt.Errorf("failed to copy to temporary table: %v", err)
			}

			// Insert from temporary table with conflict handling
			if _, err := tx.Exec(ctx, `
				INSERT INTO sol_transfers
				SELECT * FROM temp_batch
				ON CONFLICT (signature, ix_index, slot) DO NOTHING`); err != nil {
				return fmt.Errorf("failed to insert from temporary table: %v", err)
			}

			return nil
		})
		if err == nil {
			return nil
		}
		time.Sleep(time.Duration(retry+1) * 100 * time.Millisecond)
	}
	return err
}

func (r *Repo) insertTokenBatch(ctx context.Context, transfers []models.TokenTransfer) error {
	var err error
	for retry := 0; retry < maxRetries; retry++ {
		err = r.WithTransaction(ctx, func(ctx context.Context, tx pgx.Tx) error {
			// Create temporary table
			if _, err := tx.Exec(ctx, "CREATE TEMPORARY TABLE temp_batch (LIKE token_transfers) ON COMMIT DROP"); err != nil {
				return fmt.Errorf("failed to create temporary table: %v", err)
			}

			// Copy data to temporary table
			rows := make([][]interface{}, len(transfers))
			for i, t := range transfers {
				rows[i] = []interface{}{
					t.Signature,
					t.IxIndex,
					t.Slot,
					t.TokenMint,
					t.FromWallet,
					t.ToWallet,
					t.Amount,
				}
			}

			_, err = tx.CopyFrom(ctx,
				pgx.Identifier{"temp_batch"},
				[]string{"signature", "ix_index", "slot", "token_mint", "from_wallet", "to_wallet", "amount"},
				pgx.CopyFromRows(rows))
			if err != nil {
				return fmt.Errorf("failed to copy to temporary table: %v", err)
			}

			// Insert from temporary table with conflict handling
			if _, err := tx.Exec(ctx, `
				INSERT INTO token_transfers
				SELECT * FROM temp_batch
				ON CONFLICT (signature, ix_index, slot) DO NOTHING`); err != nil {
				return fmt.Errorf("failed to insert from temporary table: %v", err)
			}

			return nil
		})
		if err == nil {
			return nil
		}
		time.Sleep(time.Duration(retry+1) * 100 * time.Millisecond)
	}
	return err
}

// GetTokenBalanceChanges returns the sum of token transfers for given wallets and token mint within a slot range
func (r *Repo) GetTokenBalanceChanges(ctx context.Context, wallets [][]byte, tokenMint []byte, startSlot, endSlot uint64) (int64, error) {
	query := `
		SELECT COALESCE(SUM(amount), 0) as total
		FROM token_transfers
		WHERE to_wallet = ANY($1)
		AND token_mint = $2
		AND slot >= $3 AND slot <= $4`

	var totalIn int64
	err := r.pool.QueryRow(ctx, query, wallets, tokenMint, startSlot, endSlot).Scan(&totalIn)
	if err != nil {
		return 0, fmt.Errorf("failed to get token balance changes (in): %v", err)
	}

	query = `
		SELECT COALESCE(SUM(amount), 0) as total
		FROM token_transfers
		WHERE from_wallet = ANY($1)
		AND token_mint = $2
		AND slot >= $3 AND slot <= $4`

	var totalOut int64
	err = r.pool.QueryRow(ctx, query, wallets, tokenMint, startSlot, endSlot).Scan(&totalOut)
	if err != nil {
		return 0, fmt.Errorf("failed to get token balance changes (out): %v", err)
	}

	return totalIn - totalOut, nil
}

// GetSolBalanceChanges returns the sum of SOL transfers for given wallets within a slot range
func (r *Repo) GetSolBalanceChanges(ctx context.Context, wallets [][]byte, startSlot, endSlot uint64) (int64, error) {
	query := `
		SELECT COALESCE(SUM(amount), 0) as total
		FROM sol_transfers
		WHERE to_wallet = ANY($1)
		AND slot >= $2 AND slot <= $3`

	var totalIn int64
	err := r.pool.QueryRow(ctx, query, wallets, startSlot, endSlot).Scan(&totalIn)
	if err != nil {
		return 0, fmt.Errorf("failed to get SOL balance changes (in): %v", err)
	}

	query = `
		SELECT COALESCE(SUM(amount), 0) as total
		FROM sol_transfers
		WHERE from_wallet = ANY($1)
		AND slot >= $2 AND slot <= $3`

	var totalOut int64
	err = r.pool.QueryRow(ctx, query, wallets, startSlot, endSlot).Scan(&totalOut)
	if err != nil {
		return 0, fmt.Errorf("failed to get SOL balance changes (out): %v", err)
	}

	return totalIn - totalOut, nil
}
